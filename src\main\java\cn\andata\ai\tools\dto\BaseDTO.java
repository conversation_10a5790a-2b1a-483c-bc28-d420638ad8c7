package cn.andata.ai.tools.dto;

import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

@Data
public class BaseDTO<T> {

    @JsonPropertyDescription(value = "请求ID")
    private String reqId;

    @JsonPropertyDescription(value = "响应码")
    private String returnCode;

    @JsonPropertyDescription(value = "响应消息")
    private String returnMsg;

    @JsonPropertyDescription(value = "响应数据")
    private T result;

}