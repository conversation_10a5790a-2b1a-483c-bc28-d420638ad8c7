package cn.andata.ai.tools.dto;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.ai.util.json.schema.JsonSchemaGenerator;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonClassDescription(value = "应用分析响应体")
public class AppInfoDTO extends BaseDTO<List<AppInfoDTO.ResultItem>> {

    @Getter
    @JsonIgnore
    private static final String schema;

    static {
        schema = JsonSchemaGenerator.generateForType(AppInfoDTO.class, JsonSchemaGenerator.SchemaOption.ALLOW_ADDITIONAL_PROPERTIES_BY_DEFAULT);
    }

    @Data
    @JsonClassDescription(value = "应用信息")
    public static class ResultItem {

        @JsonPropertyDescription(value = "设备唯一标识")
        private String jdid;

        @JsonPropertyDescription(value = "应用名称")
        private String name;

        @JsonPropertyDescription(value = "应用包名")
        private String pkg;

        @JsonPropertyDescription(value = "应用版本")
        private String appVer;

        @JsonPropertyDescription(value = "应用一级分类")
        private String clsOne;

        @JsonPropertyDescription(value = "应用二级分类")
        private String clsTwo;

        @JsonPropertyDescription(value = "应用图标URL")
        private String icon;

        @JsonPropertyDescription(value = "首次安装时间")
        private String firstReportTimeTruly;

        @JsonPropertyDescription(value = "末次安装时间")
        private String firstReportTime;

        @JsonPropertyDescription(value = "最后活跃时间")
        private String lastReportTime;

        @JsonPropertyDescription(value = "卸载次数")
        private String uninstallTimes;

        @JsonPropertyDescription(value = "当前安装状态描述")
        private String statusCname;

        @JsonPropertyDescription(value = "敏感标签列表，多个标签用逗号隔开")
        private String appTagNameList;

        @JsonPropertyDescription(value = "是否系统应用 Y:是 N:否")
        private String isSystemApp;

        @JsonPropertyDescription(value = "是否小众应用 Y:是 N:否")
        private String isSmallApp;

        @JsonPropertyDescription(value = "上架状态描述")
        private String upperStatusDesc;

        @JsonPropertyDescription(value = "安装设备数")
        private String allUserCnt;
    }

}