package cn.andata.ai.common;

import lombok.Getter;

import java.io.Serial;

/**
 * 统一应用异常类
 *
 * <AUTHOR>
 */
@Getter
public class AppException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private final ErrorCode errorCode;

    /**
     * 自定义错误消息
     */
    private final String customMessage;


    public AppException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.customMessage = null;
    }

    public AppException(ErrorCode errorCode, String customMessage) {
        super(customMessage);
        this.errorCode = errorCode;
        this.customMessage = customMessage;
    }

    public AppException(String message) {
        super(message);
        this.errorCode = ErrorCode.BUSINESS_ERROR;
        this.customMessage = message;
    }

    public AppException(ErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.errorCode = errorCode;
        this.customMessage = null;
    }

    public AppException(ErrorCode errorCode, String customMessage, Throwable cause) {
        super(customMessage, cause);
        this.errorCode = errorCode;
        this.customMessage = customMessage;
    }

    public AppException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = ErrorCode.BUSINESS_ERROR;
        this.customMessage = message;
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 创建异常（使用错误码）
     */
    public static AppException of(ErrorCode errorCode) {
        return new AppException(errorCode);
    }

    /**
     * 创建异常（使用错误码和自定义消息）
     */
    public static AppException of(ErrorCode errorCode, String message) {
        return new AppException(errorCode, message);
    }

    /**
     * 创建异常（使用自定义消息）
     */
    public static AppException of(String message) {
        return new AppException(message);
    }

    /**
     * 创建异常（使用错误码和原因）
     */
    public static AppException of(ErrorCode errorCode, Throwable cause) {
        return new AppException(errorCode, cause);
    }

    /**
     * 创建异常（使用错误码、自定义消息和原因）
     */
    public static AppException of(ErrorCode errorCode, String message, Throwable cause) {
        return new AppException(errorCode, message, cause);
    }

    /**
     * 创建异常（使用自定义消息和原因）
     */
    public static AppException of(String message, Throwable cause) {
        return new AppException(message, cause);
    }

    // ==================== Getter方法 ====================

    /**
     * 获取错误码
     */
    public int getCode() {
        return errorCode.getCode();
    }

    /**
     * 获取最终的错误消息
     */
    public String getFinalMessage() {
        return customMessage != null ? customMessage : errorCode.getMessage();
    }
}