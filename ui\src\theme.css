/* 禁用所有动画和过渡效果 */
/* * {
  transition: none !important;
  animation: none !important;
} */

/* 全局字体配置 */
:root {
    --font-family-base: -apple-system, "PingFang SC", "Microsoft YaHei",
    "Segoe UI", "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-family-code: "SF Mono", SFMono-Regular, ui-monospace, "Cascadia Mono",
    "Segoe UI Mono", "Liberation Mono", Menlo, Monaco, Consolas, monospace;
}

/* 应用字体 */
body {
    margin: 0;
    padding: 0;
    font-family: var(--font-family-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* 默认浅色主题 */
    background-color: #ffffff;
}

/* 代码字体 */
code,
pre,
.monaco-editor {
    font-family: var(--font-family-code);
}

body.dark-theme {
    background-color: #141414;
}

/* 滚动条样式，适应深色/浅色主题 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

body.dark-theme ::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
}