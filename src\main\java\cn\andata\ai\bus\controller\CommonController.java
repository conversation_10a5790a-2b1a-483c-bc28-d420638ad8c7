package cn.andata.ai.bus.controller;

import cn.andata.ai.bus.entity.response.ModelInfo;
import cn.andata.ai.common.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/7/31 11:26
 */
@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
public class CommonController {

    @GetMapping("models")
    public Result<List<ModelInfo>> getModels() {
        ModelInfo deepseekModelInfo = new ModelInfo("deepseek-r1:761b", "包含 671B 参数，在后训练阶段大规模使用了强化学习技术，在仅有极少标注数据的情况下，极大提升了模型推理能力，尤其在数学、代码、自然语言推理等任务上");
        return Result.ok(List.of(deepseekModelInfo));
    }

}