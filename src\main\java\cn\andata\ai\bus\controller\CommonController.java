package cn.andata.ai.bus.controller;

import cn.andata.ai.bus.entity.response.ModelInfo;
import cn.andata.ai.common.Result;
import cn.andata.ai.tools.KeplerTools;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/7/31 11:26
 */
@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
public class CommonController {

    private final KeplerTools tools;

    @GetMapping("models")
    public Result<List<ModelInfo>> getModels() {
        ModelInfo deepseekModelInfo = new ModelInfo("deepseek-r1:761b", "包含 671B 参数，在后训练阶段大规模使用了强化学习技术，在仅有极少标注数据的情况下，极大提升了模型推理能力，尤其在数学、代码、自然语言推理等任务上");
        return Result.ok(List.of(deepseekModelInfo));
    }

    @GetMapping("/tool/{name}")
    public Result<?> invokeTool(@PathVariable String name) {
        if (StrUtil.isBlank(name)) {
            return Result.error("请指定工具名称");
        }

        Object res = switch (name) {
            case "baseInfo" -> tools.baseInfo(name);
            case "trackInfo" -> tools.trackInfo(name);
            case "historyWifiInfo" -> tools.historyWifiInfo(name);
            case "deviceInfo" -> tools.deviceInfo(name);
            case "appInfo" -> tools.appInfo(name);
            default -> Result.error("无效的工具名称");
        };

        return Result.ok(res);
    }

}