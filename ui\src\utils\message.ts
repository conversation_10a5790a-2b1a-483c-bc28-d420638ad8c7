import { Message } from "@/pages/FunctionCallingPage/types";
import {ChatMessage} from "@/types";

export const decoder = new TextDecoder("utf-8");

// 聊天持久化消息映射为UI表示
export const mapStoredMessagesToUIMessages = (
  messages: ChatMessage[]
): Message[] => {
  if (!messages || !Array.isArray(messages)) {
    console.warn("无效的消息数组:", messages);
    return [];
  }

  return (
    messages
      ?.filter((msg) => !msg.isLoading) // 过滤掉加载中的消息
      ?.map((msg) => {
        return {
          id: `msg-${msg.timestamp}`,
          text: msg.content || "",
          sender: msg.role === "user" ? "user" : "bot",
          timestamp: msg.timestamp,
        };
      }) ?? []
  );
};