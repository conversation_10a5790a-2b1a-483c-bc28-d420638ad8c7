package cn.andata.ai.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统一错误码枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ErrorCode {

    // ==================== 成功状态 ====================
    SUCCESS(200, "操作成功"),

    // ==================== 客户端错误 4xx ====================
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "不支持的请求方法"),

    // ==================== 业务错误 1xxx ====================
    BUSINESS_ERROR(1000, "业务处理失败"),
    VALIDATION_ERROR(1001, "数据验证失败"),
    DUPLICATE_ERROR(1002, "数据重复"),
    DATA_NOT_FOUND(1003, "数据不存在"),
    OPERATION_NOT_ALLOWED(1004, "操作不被允许"),

    // ==================== 系统错误 5xxx ====================
    INTERNAL_ERROR(5000, "系统内部错误"),
    SERVICE_UNAVAILABLE(5001, "服务不可用"),
    TIMEOUT(5002, "请求超时"),
    DATABASE_ERROR(5003, "数据库操作失败"),

    // ==================== 微信相关错误 6xxx ====================
    WECHAT_AUTH_ERROR(6001, "微信授权失败"),
    WECHAT_API_ERROR(6002, "微信接口调用失败"),
    WECHAT_SIGNATURE_ERROR(6003, "微信签名验证失败"),

    // ==================== 业务特定错误 7xxx ====================
    USER_NOT_FOUND(7001, "用户不存在"),
    USER_ALREADY_EXISTS(7002, "用户已存在"),
    CHECKIN_FAILED(7003, "打卡失败"),
    QR_CODE_EXPIRED(7004, "二维码已过期");

    private final int code;
    private final String message;

    /**
     * 获取格式化的错误消息
     */
    public String getMessage(Object... args) {
        return String.format(message, args);
    }

    /**
     * 根据错误码获取枚举
     */
    public static ErrorCode getByCode(int code) {
        for (ErrorCode errorCode : values()) {
            if (errorCode.getCode() == code) {
                return errorCode;
            }
        }
        return INTERNAL_ERROR;
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
}