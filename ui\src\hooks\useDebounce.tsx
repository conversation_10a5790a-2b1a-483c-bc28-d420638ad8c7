import {useEffect, useRef} from "react";

export const useDebounce = <T extends (...args: any[]) => any>(
    fn: T,
    delay: number
): T => {
    const timeoutRef = useRef<number | undefined>(undefined);

    const debouncedFn = (...args: Parameters<T>) => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(() => {
            fn(...args);
        }, delay) as unknown as number;
    };

    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    return debouncedFn as T;
};