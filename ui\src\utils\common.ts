// DOM操作相关工具函数
export const scrollToBottom = (container: HTMLElement | null) => {
  if (!container) return;
  requestAnimationFrame(() => {
    const lastMessage = container.lastElementChild as HTMLElement;

    if (lastMessage) {
      // 使用平滑滚动
      lastMessage.scrollIntoView({ behavior: "smooth", block: "end" });
    }
  });
};

// 节流函数
export const throttle = (func: (...args: any[]) => void, time: number) => {
  let inThrottle: boolean;
  return function (...args: any[]) {
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), time);
    }
  };
};
