import {atom, useAtom} from "jotai";
import {getModels} from "@/api/base";
import {ModelOption} from "@/types";

const modelOptionListAtom = atom<ModelOption[]>([]);
const currentModelAtom = atom<ModelOption>();

export const useModelConfigContext = () => {
    const [modelOptionList, setModelOptionList] = useAtom(modelOptionListAtom);
    const [currentModel, setCurrentModel] = useAtom(currentModelAtom);

    const updateModelOptionList = (list: ModelOption[]) => {
        console.log("Updating model option list:", list);
        setModelOptionList(list);
    };

    const chooseModel = (modelName: string) => {
        console.log("chooseModel", modelName, "modelOptionList:", modelOptionList);

        if (!modelName) {
            console.warn("No model name provided");
            return;
        }

        const targetModel = modelOptionList.find(
            (item) => item.value === modelName
        );

        console.log("targetModel found:", targetModel);

        const formattedModel = {
            label: targetModel?.label || modelName,
            value: targetModel?.value || modelName,
            desc: targetModel?.desc || "",
        };

        console.log("Setting current model:", formattedModel);
        setCurrentModel(formattedModel);
    };

    const initModelOptionList = async () => {
        try {
            const list = await getModels();
            console.log("list", list);

            // 确保 list 是数组格式
            const listArray = Array.isArray(list) ? list : Array.from(list);
            console.log("listArray", listArray);

            if (listArray.length === 0) {
                console.warn("No models available");
                return;
            }

            const formattedList = listArray.map((item) => {
                return {
                    label: item.model,
                    value: item.model,
                    desc: item.desc,
                };
            });

            console.log("formattedList", formattedList);
            updateModelOptionList(formattedList);

            // 使用 setTimeout 确保状态更新完成后再选择模型
            if (formattedList.length > 0) {
                setTimeout(() => {
                    console.log("Selecting default model:", formattedList[0].value);
                    chooseModel(formattedList[0].value);
                }, 0);
            }
        } catch (error) {
            console.error("Failed to initialize model list:", error);
        }
    };

    // 提供一个安全的选择模型方法，确保模型列表已加载
    const safeChooseModel = (modelName: string) => {
        if (modelOptionList.length === 0) {
            console.warn("Model list not loaded yet, deferring model selection");
            // 如果模型列表还没加载，延迟选择
            setTimeout(() => safeChooseModel(modelName), 100);
            return;
        }
        chooseModel(modelName);
    };

    return {
        currentModel,
        modelOptionList,
        chooseModel,
        safeChooseModel,
        setModelOptionList,
        initModelOptionList,
    };
};