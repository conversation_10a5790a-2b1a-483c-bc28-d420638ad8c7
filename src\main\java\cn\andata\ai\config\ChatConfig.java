package cn.andata.ai.config;

import cn.andata.ai.tools.KeplerTools;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static cn.andata.ai.config.PromptTemplate.SYSTEM_PROMPT;

/**
 * <AUTHOR>
 * @since 2025/7/15 15:33
 */
@Configuration
public class ChatConfig {

    @Bean
    public ChatClient chatClient(OllamaChatModel model, ChatMemory chatMemory, KeplerTools tools) {
        return ChatClient.builder(model)
                .defaultSystem(SYSTEM_PROMPT)
                .defaultTools(tools)
                .defaultAdvisors(
                        new SimpleLoggerAdvisor()
//                        MessageChatMemoryAdvisor.builder(chatMemory).build()
                )
                .build();
    }

}