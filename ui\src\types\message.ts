/**
 * 基础消息接口，定义所有消息类型必须包含的字段
 */
export interface BaseMessage {
    role: "user" | "assistant";
    content: string;
    timestamp: number;
    isError?: boolean;
    isLoading?: boolean;
}

/**
 * UI消息接口，用于前端显示
 */
export interface Message {
    id: string;
    text: string;
    sender: "user" | "bot";
    timestamp: number;
    isError?: boolean;
}

/**
 * 生成图像的数据结构
 */
export interface GeneratedImage {
    id: string;
    url: string;
    prompt: string;
    blob?: Blob;
    dataUrl?: string;
}

/**
 * 聊天消息的数据结构
 */
export interface ChatMessage extends BaseMessage {
    images?: GeneratedImage[];
}