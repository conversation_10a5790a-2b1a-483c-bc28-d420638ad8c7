{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/x": "1.6.0", "@atom-universe/use-web-worker": "^2.1.1", "@types/react-syntax-highlighter": "^15.5.13", "antd": "^5.26.7", "antd-style": "^3.7.1", "framer-motion": "^12.23.12", "jotai": "^2.12.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-masonry-css": "^1.0.16", "react-router-dom": "^7.7.1", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@types/node": "^24.1.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.7.0", "concurrently": "^9.2.0", "globals": "^16.3.0", "typescript": "^5.8.3", "vite": "^7.0.6"}}