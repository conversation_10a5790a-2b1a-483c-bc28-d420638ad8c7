server:
  port: 8080

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************************************************************************************************
    username: root
    password: 123456
  ai:
    ollama:
      base-url: http://*************:11434
      chat:
        options:
          model: deepseek-r1:671b
          temperature: 0.7
#          tool-names: baseInfo,deviceInfo,historyWifiInfo,trackInfo,appInfo

    chat:
      memory:
        repository:
          jdbc:
            platform: mariadb
            initialize-schema: always

tool:
  kepler:
    base-url: https://test-kepler.andataoa.com/api
    auth-key: M3ppNWdvZGRmRUlBMFVDYllaTFlTQnpSdVpBUXo2MzE6NkhJam1EZ0hTN1hGN3BVZ0wwZnNEM3ZsOTlUaWh1N0EyZXBmUnlYcnhMMlhYYlpsME52NjlvRDNyTVliMzk3Rg==

logging:
  level:
    org.springframework.ai: debug
    org.springframework.ai.chat.metadata: info

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  global-config:
    banner: off