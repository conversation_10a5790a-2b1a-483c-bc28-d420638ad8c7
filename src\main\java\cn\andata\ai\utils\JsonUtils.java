package cn.andata.ai.utils;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.andata.ai.common.AppException;
import cn.andata.ai.core.JacksonModuleFactory;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
public class JsonUtils {
    protected static final int LOG_LENGTH_LMT = 1024 * 4;

    // 私有构造函数以防止实例化
    private JsonUtils() {
        throw new UnsupportedOperationException("Utility class");
    }

    private static class ObjectMapperHolder {
        static final ObjectMapper INSTANCE = createObjectMapper();

        private static ObjectMapper createObjectMapper() {
            ObjectMapper objectMapper;
            try {
                objectMapper = SpringUtil.getBean("objectMapper", ObjectMapper.class);
            } catch (Exception e) {
                objectMapper = JacksonModuleFactory.createObjectMapper();
            }
            return objectMapper;
        }

    }

    public static ObjectMapper getObjectMapper() {
        return ObjectMapperHolder.INSTANCE;
    }


    public static String object2Json(Object o) {
        return object2Json(o, false);
    }

    public static String object2Json(Object o, boolean pretty) {
        if (o == null) {
            return null;
        }
        try {
            ObjectMapper objectMapper = getObjectMapper();
            if (pretty) {
                return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(o);
            } else {
                return objectMapper.writeValueAsString(o);
            }
        } catch (JsonProcessingException e) {
            throw AppException.of("不能序列化对象为Json", e);
        }
    }

    @SuppressWarnings("unchecked")
    public static Map<String, Object> object2Map(Object o) {
        return getObjectMapper().convertValue(o, Map.class);
    }

    public static <T> T json2Object(String json, Class<T> clazz) {
        if (StrUtil.isBlank(json)) {
            return null;
        }
        try {
            return getObjectMapper().readValue(json, clazz);
        } catch (Exception e) {
            throw AppException.of("将Json转换为对象时异常, json串为 " + getLoggerString(json), e);
        }
    }

    private static void checkNotBlank(String json) {
        if (StrUtil.isBlank(json)) {
            throw AppException.of("json字符串为空");
        }
    }

    public static <T> T json2Object(String json, TypeReference<T> typeReference) {
        if (StrUtil.isBlank(json)) {
            return null;
        }
        try {
            return getObjectMapper().readValue(json, typeReference);
        } catch (Exception e) {
            log.error("将Json转换为对象时异常, json串为{}", getLoggerString(json), e);
            throw AppException.of("将Json转换为对象时异常", e);
        }
    }

    public static <T> T json2Object(String json, JavaType javaType) {
        if (StrUtil.isBlank(json)) {
            return null;
        }
        try {
            return getObjectMapper().readValue(json, javaType);
        } catch (Exception e) {
            log.error("将Json转换为对象时异常, json串为{}", getLoggerString(json), e);
            throw AppException.of("将Json转换为对象时异常", e);
        }
    }

    public static <T> T json2Object(InputStream in, Class<T> clazz) {
        String json = null;
        try {
            json = IoUtil.read(in, StandardCharsets.UTF_8);
            return getObjectMapper().readValue(json, clazz);
        } catch (Exception e) {
            throw AppException.of("将 Json 转换为对象时异常, 数据是: " + getLoggerString(json), e);
        }
    }

    public static <T> T json2Object(Reader reader, Class<T> clazz) {
        String json = null;
        try {
            json = IoUtil.read(reader);
            return getObjectMapper().readValue(json, clazz);
        } catch (Exception e) {
            throw AppException.of("将 Json 转换为对象时异常, 数据是: " + getLoggerString(json), e);
        }
    }

    public static JavaType getCollectionType(Class<?> collectionClass, Class<?>... elementClasses) {
        try {
            return getObjectMapper().getTypeFactory().constructParametricType(collectionClass, elementClasses);
        } catch (Exception e) {
            throw AppException.of("获取集合类型异常", e);
        }
    }

    public static <T> List<T> json2List(String json, Class<T> clazz) {
        if (StrUtil.isBlank(json)) {
            return Collections.emptyList();
        }
        try {
            return getObjectMapper().readValue(json, getCollectionType(List.class, clazz));
        } catch (JsonProcessingException e) {
            throw AppException.of("将Json转换为集合时异常, json: " + getLoggerString(json), e);
        }
    }

    private static String getLoggerString(String json) {
        return StrUtil.sub(json, 0, LOG_LENGTH_LMT);
    }

    public static <T> T convertValue(Object data, Class<T> clazz) {
        return getObjectMapper().convertValue(data, clazz);
    }
}