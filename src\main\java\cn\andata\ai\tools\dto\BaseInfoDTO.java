package cn.andata.ai.tools.dto;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.ai.util.json.schema.JsonSchemaGenerator;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonClassDescription(value = "基础信息查询响应体")
public class BaseInfoDTO extends BaseDTO<BaseInfoDTO.Result> {

    @Getter
    @JsonIgnore
    private static final String schema;

    static {
        schema = JsonSchemaGenerator.generateForType(BaseInfoDTO.class, JsonSchemaGenerator.SchemaOption.ALLOW_ADDITIONAL_PROPERTIES_BY_DEFAULT);
    }


    @Data
    @JsonClassDescription(value = "基础信息查询结果")
    public static class Result {

        @JsonPropertyDescription(value = "设备唯一标识")
        private String jdid;

        @JsonPropertyDescription(value = "相关度")
        private String sourceCertainty;

        @JsonPropertyDescription(value = "机卡关系")
        private String relativeRelation;

        @JsonPropertyDescription(value = "设备特征信息")
        private DeviceFeatureInfo deviceFeatureInfo;

        @JsonPropertyDescription(value = "位置信息")
        private AddressInfo addressInfo;

        @JsonPropertyDescription(value = "安卓设备最新轨迹信息")
        private LatestTraceLbsInfo latestTraceLbsInfo;

        @JsonPropertyDescription(value = "最新 IP 信息")
        private LatestIpInfo latestIpInfo;

        @JsonPropertyDescription(value = "设备标签")
        private List<String> deviceTag;

        @JsonPropertyDescription(value = "是否为苹果设备")
        private boolean iphone;
    }

    @Data
    @JsonClassDescription(value = "设备特征信息")
    public static class DeviceFeatureInfo {

        @JsonPropertyDescription(value = "IMEI，多个用竖线分隔")
        private String imei;

        @JsonPropertyDescription(value = "手机号")
        private String phone;

        @JsonPropertyDescription(value = "MAC 地址")
        private String mac;

        @JsonPropertyDescription(value = "IMSI，多个用竖线分隔")
        private String imsi;

        @JsonPropertyDescription(value = "OAID")
        private String oaid;

        @JsonPropertyDescription(value = "IDFA")
        private String idfa;

        @JsonPropertyDescription(value = "MAC 是否变换")
        private String macChange;

        @JsonPropertyDescription(value = "运营商")
        private String isp;

        @JsonPropertyDescription(value = "归属地")
        private String qCellCore;

        @JsonPropertyDescription(value = "设备型号")
        private String deviceModel;

        @JsonPropertyDescription(value = "品牌")
        private String brand;

        @JsonPropertyDescription(value = "终端类型")
        private String terminalType;

        @JsonPropertyDescription(value = "系统版本")
        private String version;

        @JsonPropertyDescription(value = "语言")
        private String language;

        @JsonPropertyDescription(value = "时区")
        private String timeZone;
    }

    @Data
    @JsonClassDescription(value = "地址信息")
    public static class AddressInfo {

        @JsonPropertyDescription(value = "常驻地地址")
        private String homeAddress;

        @JsonPropertyDescription(value = "常驻地 POI 类型")
        private String homePoiType;
    }

    @Data
    @JsonClassDescription(value = "最新轨迹信息")
    public static class LatestTraceLbsInfo {

        @JsonPropertyDescription(value = "上报时间")
        private String itime;

        @JsonPropertyDescription(value = "IP 地址")
        private String ip;

        @JsonPropertyDescription(value = "坐标来源（GPS、WIFI_SSID、CELL、IP）")
        private String coordinateSource;

        @JsonPropertyDescription(value = "WGS84 坐标经纬度")
        private String coordinate;

        @JsonPropertyDescription(value = "高德坐标经纬度")
        private String gdCoordinate;

        @JsonPropertyDescription(value = "国家")
        private String country;

        @JsonPropertyDescription(value = "省份")
        private String province;

        @JsonPropertyDescription(value = "城市")
        private String city;

        @JsonPropertyDescription(value = "区")
        private String district;

        @JsonPropertyDescription(value = "镇")
        private String town;

        @JsonPropertyDescription(value = "路")
        private String road;

        @JsonPropertyDescription(value = "村")
        private String village;

        @JsonPropertyDescription(value = "详细地址")
        private String address;

        @JsonPropertyDescription(value = "POI 类型")
        private String poiType;

        @JsonPropertyDescription(value = "POI")
        private String poi;
    }

    @Data
    @JsonClassDescription(value = "最新IP信息")
    public static class LatestIpInfo {

        @JsonPropertyDescription(value = "IP 地址")
        private String ip;

        @JsonPropertyDescription(value = "上报时间")
        private String itime;

        @JsonPropertyDescription(value = "归属地")
        private String qcell;
    }
}