package cn.andata.ai.tools.dto;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.ai.util.json.schema.JsonSchemaGenerator;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonClassDescription(value = "历史WiFi信息响应")
public class HistoryWiFiInfoDTO extends BaseDTO<List<HistoryWiFiInfoDTO.Result>> {

    @Getter
    @JsonIgnore
    private static final String schema;

    static {
        schema = JsonSchemaGenerator.generateForType(HistoryWiFiInfoDTO.class, JsonSchemaGenerator.SchemaOption.ALLOW_ADDITIONAL_PROPERTIES_BY_DEFAULT);
    }


    @Data
    @JsonClassDescription(value = "历史WiFi单条信息")
    public static class Result {

        @JsonPropertyDescription(value = "设备唯一标识")
        private String jdid;

        @JsonPropertyDescription(value = "WiFi Mac地址")
        private String wifiMac;

        @JsonPropertyDescription(value = "连接时间")
        private String itime;

        @JsonPropertyDescription(value = "WiFi名称")
        private String ssid;

        @JsonPropertyDescription(value = "IP地址")
        private String ip;

        @JsonPropertyDescription(value = "连接类型 S:扫描 C:连接")
        private String connectType;

        @JsonPropertyDescription(value = "IP来源: WiFi 移动G网")
        private String source;

        @JsonPropertyDescription(value = "wgs84经纬度（经度，纬度）")
        private String location;

        @JsonPropertyDescription(value = "高德经纬度（经度，纬度）")
        private String gdLocation;

        @JsonPropertyDescription(value = "WiFi地址")
        private String address;
    }
}