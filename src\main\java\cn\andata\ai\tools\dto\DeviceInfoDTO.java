package cn.andata.ai.tools.dto;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.ai.util.json.schema.JsonSchemaGenerator;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonClassDescription(value = "设备信息查询响应体")
public class DeviceInfoDTO extends BaseDTO<DeviceInfoDTO.Result> {

    @Getter
    @JsonIgnore
    private static final String schema;

    static {
        schema = JsonSchemaGenerator.generateForType(DeviceInfoDTO.class, JsonSchemaGenerator.SchemaOption.ALLOW_ADDITIONAL_PROPERTIES_BY_DEFAULT);
    }


    @Data
    @JsonClassDescription(value = "设备信息返回结果")
    public static class Result {

        @JsonPropertyDescription(value = "设备唯一标识")
        private String jdid;

        @JsonPropertyDescription(value = "历史设备列表")
        private List<HistoryDevice> historyDeviceList;

        @JsonPropertyDescription(value = "换机换卡记录详情")
        private List<ChangeRecordDetail> changeRecordDetailList;

        @Data
        @JsonClassDescription(value = "历史设备信息")
        public static class HistoryDevice {

            @JsonPropertyDescription(value = "设备唯一标识")
            private String jdid;

            @JsonPropertyDescription(value = "相关度")
            private String sourceCertainty;

            @JsonPropertyDescription(value = "机卡关系")
            private String relativeRelation;

            @JsonPropertyDescription(value = "开始关联时间")
            private String startTime;

            @JsonPropertyDescription(value = "结束关联时间（为空表示使用至今）")
            private String endTime;

            @JsonPropertyDescription(value = "IMEI 多个用 | 分隔")
            private String imei;

            @JsonPropertyDescription(value = "IMSI 多个用 | 分隔")
            private String imsi;

            @JsonPropertyDescription(value = "MAC地址")
            private String mac;

            @JsonPropertyDescription(value = "OAID")
            private String oaid;

            @JsonPropertyDescription(value = "IDFA")
            private String idfa;

            @JsonPropertyDescription(value = "设备型号")
            private String model;

            @JsonPropertyDescription(value = "品牌")
            private String brand;

            @JsonPropertyDescription(value = "手机号码")
            private String phone;

            @JsonPropertyDescription(value = "归属地")
            private String qCellCore;

            @JsonPropertyDescription(value = "运营商")
            private String isp;
        }

        @Data
        @JsonClassDescription(value = "换机换卡详情")
        public static class ChangeRecordDetail {

            @JsonPropertyDescription(value = "设备唯一标识")
            private String jdid;

            @JsonPropertyDescription(value = "手机号")
            private String phone;

            @JsonPropertyDescription(value = "开始关联时间")
            private String startTime;

            @JsonPropertyDescription(value = "结束关联时间（为空表示使用至今）")
            private String endTime;

            @JsonPropertyDescription(value = "IMEI 多个用 | 分隔")
            private String imei;

            @JsonPropertyDescription(value = "IMSI 多个用 | 分隔")
            private String imsi;

            @JsonPropertyDescription(value = "MAC地址")
            private String mac;

            @JsonPropertyDescription(value = "OAID")
            private String oaid;

            @JsonPropertyDescription(value = "IDFA")
            private String idfa;

            @JsonPropertyDescription(value = "设备型号")
            private String model;

            @JsonPropertyDescription(value = "品牌")
            private String brand;

            @JsonPropertyDescription(value = "归属地")
            private String qCellCore;

            @JsonPropertyDescription(value = "运营商")
            private String isp;

            @JsonPropertyDescription(value = "语言")
            private String language;

            @JsonPropertyDescription(value = "系统版本")
            private String osVersion;
        }
    }
}