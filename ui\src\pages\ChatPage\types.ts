import {GeneratedImage} from "@/stores/conversation.store";
import {Message} from "@/pages/FunctionCallingPage/types";

declare global {
    interface Window {
        tempImageBase64?: string;
    }
}

export interface ChatConversationViewProps {
    conversationId: string;
}

export type {Message};

export interface ChatMessage extends Message {
    images?: GeneratedImage[];
}

export interface AiCapabilities {
    deepThink: boolean;
    onlineSearch: boolean;
}