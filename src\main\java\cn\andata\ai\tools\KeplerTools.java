package cn.andata.ai.tools;

import cn.andata.ai.tools.dto.*;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSON;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

/**
 * 开普勒研判相关工具
 *
 * <AUTHOR>
 * @since 2025/7/31 10:30
 */
@Slf4j
@Component
public class KeplerTools {

    private final RestClient restClient;

    public KeplerTools(RestClient.Builder restClientBuilder,
                       @Value("${tool.kepler.base-url}") String baseUrl,
                       @Value("${tool.kepler.auth-key}") String authKey) {
        this.restClient = restClientBuilder.baseUrl(baseUrl)
                .defaultHeader("Content-Type", "application/json")
                .defaultHeader("Authorization", "Basic " + authKey)
                .build();
    }

    /**
     * 获取完整分析数据 - 并发调用所有数据源
     *
     * @param phone 手机号
     * @return 包含所有数据源的综合分析数据
     */
    @Tool(name = "getCompleteAnalysisData", description = """
            根据手机号一次性获取所有分析数据，包括：
            - 基本信息：设备特征、位置信息、最新轨迹、IP信息、设备标签等
            - 设备信息：历史设备列表、换机换卡记录、设备关联度等
            - 轨迹信息：近3个月详细位置轨迹、坐标、地址、POI信息等
            - WiFi信息：近3个月WiFi连接和扫描历史、热点信息、位置信息等
            - 应用信息：应用安装列表、分类、敏感标签、使用行为等

            此工具使用并发调用优化性能，一次性获取所有必要的分析数据，
            适用于公安技术分析、用户行为研判、风险评估等场景。
            """)
    public CompleteAnalysisDataDTO getCompleteAnalysisData(@ToolParam(description = "手机号") String phone) {
        long startTime = System.currentTimeMillis();
        List<String> failedSources = new ArrayList<>();

        // 并发调用所有数据源
        CompletableFuture<BaseInfoDTO> baseInfoFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return baseInfoInternal(phone);
            } catch (Exception e) {
                log.error("获取基本信息失败: {}", e.getMessage(), e);
                failedSources.add("baseInfo");
                return null;
            }
        });

        CompletableFuture<DeviceInfoDTO> deviceInfoFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return deviceInfoInternal(phone);
            } catch (Exception e) {
                log.error("获取设备信息失败: {}", e.getMessage(), e);
                failedSources.add("deviceInfo");
                return null;
            }
        });

        CompletableFuture<TrackInfoDTO> trackInfoFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return trackInfoInternal(phone);
            } catch (Exception e) {
                log.error("获取轨迹信息失败: {}", e.getMessage(), e);
                failedSources.add("trackInfo");
                return null;
            }
        });

        CompletableFuture<HistoryWiFiInfoDTO> historyWifiInfoFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return historyWifiInfoInternal(phone);
            } catch (Exception e) {
                log.error("获取WiFi信息失败: {}", e.getMessage(), e);
                failedSources.add("historyWifiInfo");
                return null;
            }
        });

        CompletableFuture<AppInfoDTO> appInfoFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return appInfoInternal(phone);
            } catch (Exception e) {
                log.error("获取应用信息失败: {}", e.getMessage(), e);
                failedSources.add("appInfo");
                return null;
            }
        });

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                baseInfoFuture, deviceInfoFuture, trackInfoFuture,
                historyWifiInfoFuture, appInfoFuture
        );

        try {
            allFutures.join();
        } catch (CompletionException e) {
            log.warn("部分数据获取失败，继续处理已获取的数据", e);
        }

        // 获取结果
        BaseInfoDTO baseInfo = baseInfoFuture.join();
        DeviceInfoDTO deviceInfo = deviceInfoFuture.join();
        TrackInfoDTO trackInfo = trackInfoFuture.join();
        HistoryWiFiInfoDTO historyWifiInfo = historyWifiInfoFuture.join();
        AppInfoDTO appInfo = appInfoFuture.join();

        long endTime = System.currentTimeMillis();
        long totalDuration = endTime - startTime;

        // 计算成功率
        int totalSources = 5;
        int successfulSources = totalSources - failedSources.size();
        double successRate = (double) successfulSources / totalSources;

        // 构建数据状态
        CompleteAnalysisDataDTO.DataStatus dataStatus = CompleteAnalysisDataDTO.DataStatus.builder()
                .baseInfoSuccess(baseInfo != null)
                .deviceInfoSuccess(deviceInfo != null)
                .trackInfoSuccess(trackInfo != null)
                .historyWifiInfoSuccess(historyWifiInfo != null)
                .appInfoSuccess(appInfo != null)
                .successRate(successRate)
                .failedSources(failedSources)
                .totalDurationMs(totalDuration)
                .build();

        log.info("完整数据获取完成 - 手机号: {}, 成功率: {}%, 耗时: {}ms",
                phone, String.format("%.2f", successRate * 100), totalDuration);

        return CompleteAnalysisDataDTO.builder()
                .phone(phone)
                .timestamp(System.currentTimeMillis())
                .baseInfo(baseInfo)
                .deviceInfo(deviceInfo)
                .trackInfo(trackInfo)
                .historyWifiInfo(historyWifiInfo)
                .appInfo(appInfo)
                .dataStatus(dataStatus)
                .build();
    }



    // ==================== 内部实现方法 ====================

    /**
     * 内部方法：获取基本信息
     */
    private BaseInfoDTO baseInfoInternal(String phone) {
        return performRequest("/ext/kepler-psd-analysis/v2/base-info", Body.of("phone", phone), BaseInfoDTO.class);
    }

    /**
     * 内部方法：获取设备信息
     */
    private DeviceInfoDTO deviceInfoInternal(String phone) {
        return performRequest("/ext/kepler-psd-analysis/v2/device-info", Body.of("phone", phone), DeviceInfoDTO.class);
    }

    /**
     * 内部方法：获取轨迹信息
     */
    private TrackInfoDTO trackInfoInternal(String phone) {
        LocalDate now = LocalDate.now();
        LocalDate begin = now.minusMonths(3);
        return performRequest("/ext/kepler-psd-analysis/v2/trace/list", Body.of("phone", phone, begin, now), TrackInfoDTO.class);
    }

    /**
     * 内部方法：获取WiFi历史信息
     */
    private HistoryWiFiInfoDTO historyWifiInfoInternal(String phone) {
        LocalDate now = LocalDate.now();
        LocalDate begin = now.minusMonths(3);
        return performRequest("/ext/kepler-psd-analysis/v2/history-ip/detail/list", Body.of("phone", phone, begin, now), HistoryWiFiInfoDTO.class);
    }

    /**
     * 内部方法：获取应用信息
     */
    private AppInfoDTO appInfoInternal(String phone) {
        return performRequest("/ext/kepler-psd-analysis/v2/app/list", Body.of("phone", phone), AppInfoDTO.class);
    }


    <T> T performRequest(String uri, Body body, Class<T> clazz) {
        T res = restClient.post()
                .uri(uri)
                .body(body)
                .retrieve()
                .body(clazz);

        if (res == null) {
            throw new RuntimeException("请求失败");
        }
        log.info("请求URI: {} ,结果：{}", uri, JSON.toJSONString(res));
        return res;

    }


    @Data
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    static class Body {
        private String keyType;
        private String key;
        private String startDate;
        private String endDate;

        public static Body of(String keyType, String key) {
            return new Body(keyType, key, null, null);
        }

        public static Body of(String keyType, String key, LocalDate startDate, LocalDate endDate) {
            return new Body(keyType, key, LocalDateTimeUtil.formatNormal(startDate), LocalDateTimeUtil.formatNormal(endDate));
        }

    }

}