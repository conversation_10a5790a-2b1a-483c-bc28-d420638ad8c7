package cn.andata.ai.tools;

import cn.andata.ai.tools.dto.*;
import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import java.time.LocalDate;

/**
 * 开普勒研判相关工具
 *
 * <AUTHOR>
 * @since 2025/7/31 10:30
 */
@Slf4j
@Component
public class KeplerTools {

    private final RestClient restClient;

    public KeplerTools(RestClient.Builder restClientBuilder,
                       @Value("${tool.kepler.base-url}") String baseUrl,
                       @Value("${tool.kepler.auth-key}") String authKey) {
        this.restClient = restClientBuilder.baseUrl(baseUrl)
                .defaultHeader("Content-Type", "application/json")
                .defaultHeader("Authorization", "Basic " + authKey)
                .build();
    }

    @Tool(name = "baseInfo", description = """
            根据手机号获取用户基本信息，包括：
            - 设备特征信息：IMEI、MAC地址、IMSI、设备型号、品牌、系统版本等
            - 机卡关系：设备与手机号的绑定关系和相关度
            - 位置信息：常驻地地址、POI类型等
            - 最新轨迹：最近的位置轨迹信息，包含坐标、地址、POI等
            - 最新IP信息：最近的IP地址和归属地
            - 设备标签：系统识别的设备特征标签
            - 设备类型：是否为苹果设备等
            """)
    public BaseInfoDTO baseInfo(@ToolParam(description = "手机号") String phone) {
        return performRequest("/ext/kepler-psd-analysis/v2/base-info", Body.of("phone", phone), BaseInfoDTO.class);
    }

    @Tool(name = "deviceInfo", description = """
            根据手机号获取用户设备信息，包括：
            - 历史设备列表：该手机号历史关联的所有设备信息
            - 换机换卡记录：设备更换和SIM卡更换的详细记录
            - 设备关联度：每个设备与手机号的关联程度和可信度
            - 机卡关系变化：设备绑定关系的历史变化情况
            此数据可用于分析用户的换机行为模式，识别异常的设备更换频率
            """)
    public DeviceInfoDTO deviceInfo(@ToolParam(description = "手机号") String phone) {
        return performRequest("/ext/kepler-psd-analysis/v2/device-info", Body.of("phone", phone), DeviceInfoDTO.class);
    }

    @Tool(name = "historyWifiInfo", description = """
            根据手机号获取用户近三个月的WiFi连接和扫描历史，包括：
            - WiFi热点信息：SSID名称、MAC地址、信号强度等
            - 连接时间：首次连接时间、最后连接时间、连接频次
            - 位置信息：WiFi热点的地理位置和POI信息
            - 网络类型：家庭网络、公共WiFi、企业网络等
            - IP地址信息：通过WiFi获取的IP地址和归属地
            此数据可用于分析用户的活动区域、生活规律和网络使用习惯
            """)
    public HistoryWiFiInfoDTO historyWifiInfo(@ToolParam(description = "手机号") String phone) {
        LocalDate now = LocalDate.now();
        LocalDate begin = now.minusMonths(3);
        return performRequest("/ext/kepler-psd-analysis/v2/history-ip/detail/list", Body.of("phone", phone, begin, now), HistoryWiFiInfoDTO.class);
    }

    @Tool(name = "trackInfo", description = """
            根据手机号获取用户近三个月的详细轨迹走势，包括：
            - 位置坐标：WGS84和高德坐标系的经纬度信息
            - 时间信息：每个轨迹点的具体时间戳
            - 地址信息：国家、省市区县、详细地址等行政区划
            - POI信息：兴趣点名称和类型（如商场、学校、医院等）
            - 坐标来源：GPS、WiFi、基站、IP等定位方式
            - 网络信息：WiFi SSID、基站信息、IP地址等
            此数据可用于分析用户的活动轨迹、生活规律、常去场所和异常行为
            """)
    public TrackInfoDTO trackInfo(@ToolParam(description = "手机号") String phone) {
        LocalDate now = LocalDate.now();
        LocalDate begin = now.minusMonths(3);
        return performRequest("/ext/kepler-psd-analysis/v2/trace/list", Body.of("phone", phone, begin, now), TrackInfoDTO.class);
    }

    @Tool(name = "appInfo", description = """
            根据手机号获取用户应用安装列表和使用行为，包括：
            - 应用基本信息：应用名称、包名、版本、图标等
            - 应用分类：一级分类、二级分类，便于分析用户兴趣偏好
            - 安装行为：首次安装时间、最后活跃时间、卸载次数等
            - 敏感标签：系统识别的敏感应用标签（如金融、社交、工具等）
            - 应用特征：是否系统应用、是否小众应用、上架状态等
            - 使用统计：安装设备数等流行度指标
            此数据可用于分析用户的兴趣偏好、行为习惯和潜在风险应用
            """)
    public AppInfoDTO appInfo(@ToolParam(description = "手机号") String phone) {
        return performRequest("/ext/kepler-psd-analysis/v2/app/list", Body.of("phone", phone), AppInfoDTO.class);
    }


    @Tool(name = "schema", description = """
            获取指定工具响应结果的JSON Schema，用于理解数据结构。
            支持的工具名称：baseInfo, trackInfo, historyWifiInfo, deviceInfo, appInfo
            """)
    public String schema(@ToolParam(description = "工具名称") String toolName) {
        return switch (toolName) {
            case "baseInfo" -> BaseInfoDTO.getSchema();
            case "trackInfo" -> TrackInfoDTO.getSchema();
            case "historyWifiInfo" -> HistoryWiFiInfoDTO.getSchema();
            case "deviceInfo" -> DeviceInfoDTO.getSchema();
            case "appInfo" -> AppInfoDTO.getSchema();
            default -> null;
        };
    }

    <T> T performRequest(String uri, Body body, Class<T> clazz) {
        return restClient.post()
                .uri(uri)
                .body(body)
                .retrieve()
                .body(clazz);
    }


    @Data
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    static class Body {
        private String keyType;
        private String key;
        private String startDate;
        private String endDate;

        public static Body of(String keyType, String key) {
            return new Body(keyType, key, null, null);
        }

        public static Body of(String keyType, String key, LocalDate startDate, LocalDate endDate) {
            return new Body(keyType, key, LocalDateTimeUtil.formatNormal(startDate), LocalDateTimeUtil.formatNormal(endDate));
        }

    }

}