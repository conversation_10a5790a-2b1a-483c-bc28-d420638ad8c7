package cn.andata.ai.tools;

import cn.andata.ai.tools.dto.*;
import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import java.time.LocalDate;

/**
 * 开普勒研判相关工具
 *
 * <AUTHOR>
 * @since 2025/7/31 10:30
 */
@Slf4j
@Component
public class KeplerTools {

    private final RestClient restClient;

    public KeplerTools(RestClient.Builder restClientBuilder,
                       @Value("${tool.kepler.base-url}") String baseUrl,
                       @Value("${tool.kepler.auth-key}") String authKey) {
        this.restClient = restClientBuilder.baseUrl(baseUrl)
                .defaultHeader("Content-Type", "application/json")
                .defaultHeader("Authorization", "Basic " + authKey)
                .build();
    }

    @Tool(name = "baseInfo", description = "根据手机号获取用户基本信息")
    public BaseInfoDTO baseInfo(@ToolParam(description = "手机号") String phone) {
        return performRequest("/ext/kepler-psd-analysis/v2/base-info", Body.of("phone", phone), BaseInfoDTO.class);
    }

    @Tool(name = "deviceInfo", description = "根据手机号获取用户设备信息")
    public DeviceInfoDTO deviceInfo(@ToolParam(description = "手机号") String phone) {
        return performRequest("/ext/kepler-psd-analysis/v2/device-info", Body.of("phone", phone), DeviceInfoDTO.class);
    }

    @Tool(name = "historyWifiInfo", description = "根据手机号获取用户近三月连接或扫描WiFi信息")
    public HistoryWiFiInfoDTO historyWifiInfo(@ToolParam(description = "手机号") String phone) {
        LocalDate now = LocalDate.now();
        LocalDate begin = now.minusMonths(3);
        return performRequest("/ext/kepler-psd-analysis/v2/history-ip/detail/list", Body.of("phone", phone, begin, now), HistoryWiFiInfoDTO.class);
    }

    @Tool(name = "trackInfo", description = "根据手机号获取用户近三月轨迹走势")
    public TrackInfoDTO trackInfo(@ToolParam(description = "手机号") String phone) {
        LocalDate now = LocalDate.now();
        LocalDate begin = now.minusMonths(3);
        return performRequest("/ext/kepler-psd-analysis/v2/trace/list", Body.of("phone", phone, begin, now), TrackInfoDTO.class);
    }

//    @Tool(name = "appInfo", description = "根据手机号获取用户应用安装列表")
//    public AppInfoDTO appInfo(@ToolParam(description = "手机号") String phone) {
//        return performRequest("/ext/kepler-psd-analysis/v2/app/list", Body.of("phone", phone), AppInfoDTO.class);
//    }


    @Tool(name = "schema", description = "工具响应结果的schema")
    public String schema(@ToolParam(description = "工具名称") String toolName) {
        return switch (toolName) {
            case "baseInfo" -> BaseInfoDTO.getSchema();
            case "trackInfo" -> TrackInfoDTO.getSchema();
            case "historyWifiInf" -> HistoryWiFiInfoDTO.getSchema();
            case "deviceInfo" -> DeviceInfoDTO.getSchema();
            case "appInfo" -> AppInfoDTO.getSchema();
            default -> null;
        };
    }

    <T> T performRequest(String uri, Body body, Class<T> clazz) {
        return restClient.post()
                .uri(uri)
                .body(body)
                .retrieve()
                .body(clazz);
    }


    @Data
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    static class Body {
        private String keyType;
        private String key;
        private String startDate;
        private String endDate;

        public static Body of(String keyType, String key) {
            return new Body(keyType, key, null, null);
        }

        public static Body of(String keyType, String key, LocalDate startDate, LocalDate endDate) {
            return new Body(keyType, key, LocalDateTimeUtil.formatNormal(startDate), LocalDateTimeUtil.formatNormal(endDate));
        }

    }

}