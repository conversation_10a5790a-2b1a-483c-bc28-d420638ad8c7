import { atom, useAtom } from "jotai";
import { FunctionMenuItem } from "@/types";

// 定义 atom 并指定类型
const activeMenuPageAtom = atom<string | null>(null);
const inputtingContentAtom = atom<string | null>(null);
const menuCollapsedAtom = atom<boolean>(false);

export enum MenuPage {
    Chat = "chat",
    ImageGen = "image-gen",
    DocSummary = "doc-summary",
    MultiModal = "multi-modal",
    ToolCalling = "tool-calling",
    Rag = "rag",
    Mcp = "mcp",
    MoreExamples = "more-examples",
}

export const useFunctionMenuStore = () => {
    const [activeMenuPage, setActiveMenuPage] = useAtom(activeMenuPageAtom);
    const [inputtingContent, setInputtingContent] = useAtom(inputtingContentAtom);
    const [menuCollapsed, setMenuCollapsed] = useAtom(menuCollapsedAtom);

    const updateInputtingContent = (content: string) => {
        console.log("Store: 更新输入内容 ->", content);
        setInputtingContent(content);
    };

    const updateActiveMenuPage = (menuPageItem: FunctionMenuItem) => {
        setInputtingContent(null);
        setActiveMenuPage(menuPageItem.key);
    };

    const chooseActiveMenuPage = (key: MenuPage) => {
        setActiveMenuPage(key);
    };

    const toggleMenuCollapsed = () => {
        setMenuCollapsed((prev) => !prev); // 使用函数式更新
    };

    return {
        activeMenuPage,
        inputtingContent,
        menuCollapsed,
        updateActiveMenuPage,
        chooseActiveMenuPage,
        updateInputtingContent,
        toggleMenuCollapsed,
    };
};