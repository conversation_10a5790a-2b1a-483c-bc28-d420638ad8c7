<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
  <!-- 禁用 Logback 启动日志 -->
  <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>
  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

  <!-- 日志格式定义 -->
  <property name="PATTERN"
            value="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p %X{user} %X{traceId:- } [%15.15t] %-50.50(%logger{36}.%method#%line): %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

  <property name="CONSOLE_PATTERN"
            value="%magenta(%d{yyyy-MM-dd HH:mm:ss.SSS}) %highlight(%-5level) %X{user} %clr(%X{traceId}){faint} %clr([%12.12t]){faint} %clr(%-50.50(%logger{36}.%method#%line)){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

  <!-- 外部属性 -->
  <springProperty scope="context" name="LOG_DIR" source="logging.path" defaultValue="logs"/>
  <springProperty scope="context" name="MAX_HISTORY" source="logging.max-history" defaultValue="30"/>
  <springProperty scope="context" name="MAX_FILE_SIZE" source="logging.max-file-size" defaultValue="1GB"/>
  <springProperty scope="context" name="BUFFER_SIZE" source="logging.buffer-size" defaultValue="65536"/>
  <springProperty scope="context" name="FLUSH_TIMEOUT" source="logging.max-flush-time" defaultValue="10000"/>

  <!-- 控制台输出 -->
  <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>${CONSOLE_PATTERN}</pattern>
    </encoder>
  </appender>
  <appender name="async_stdout" class="ch.qos.logback.classic.AsyncAppender">
    <discardingThreshold>0</discardingThreshold>
    <queueSize>${BUFFER_SIZE}</queueSize>
    <maxFlushTime>${FLUSH_TIMEOUT}</maxFlushTime>
    <includeCallerData>true</includeCallerData>
    <appender-ref ref="stdout"/>
  </appender>

  <!-- TRACE 级别日志 -->
  <appender name="trace" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_DIR}/trace/trace.log</file>
    <encoder>
      <pattern>${PATTERN}</pattern>
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${LOG_DIR}/trace/trace-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <maxHistory>${MAX_HISTORY}</maxHistory>
      <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
    </rollingPolicy>
  </appender>
  <appender name="async_trace" class="ch.qos.logback.classic.AsyncAppender">
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>TRACE</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <discardingThreshold>0</discardingThreshold>
    <queueSize>${BUFFER_SIZE}</queueSize>
    <maxFlushTime>${FLUSH_TIMEOUT}</maxFlushTime>
    <includeCallerData>true</includeCallerData>
    <appender-ref ref="trace"/>
  </appender>

  <!-- DEBUG 级别日志 -->
  <appender name="debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_DIR}/debug/debug.log</file>
    <encoder>
      <pattern>${PATTERN}</pattern>
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${LOG_DIR}/debug/debug-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <maxHistory>${MAX_HISTORY}</maxHistory>
      <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
    </rollingPolicy>
  </appender>
  <appender name="async_debug" class="ch.qos.logback.classic.AsyncAppender">
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>DEBUG</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <discardingThreshold>0</discardingThreshold>
    <queueSize>${BUFFER_SIZE}</queueSize>
    <maxFlushTime>${FLUSH_TIMEOUT}</maxFlushTime>
    <includeCallerData>true</includeCallerData>
    <appender-ref ref="debug"/>
  </appender>

  <!-- INFO 级别日志 -->
  <appender name="info" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_DIR}/info/info.log</file>
    <encoder>
      <pattern>${PATTERN}</pattern>
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${LOG_DIR}/info/info-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <maxHistory>${MAX_HISTORY}</maxHistory>
      <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
    </rollingPolicy>
  </appender>
  <appender name="async_info" class="ch.qos.logback.classic.AsyncAppender">
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>INFO</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <discardingThreshold>0</discardingThreshold>
    <queueSize>${BUFFER_SIZE}</queueSize>
    <maxFlushTime>${FLUSH_TIMEOUT}</maxFlushTime>
    <includeCallerData>true</includeCallerData>
    <appender-ref ref="info"/>
  </appender>

  <!-- WARN 级别日志 -->
  <appender name="warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_DIR}/warn/warn.log</file>
    <encoder>
      <pattern>${PATTERN}</pattern>
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${LOG_DIR}/warn/warn-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <maxHistory>${MAX_HISTORY}</maxHistory>
      <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
    </rollingPolicy>
  </appender>
  <appender name="async_warn" class="ch.qos.logback.classic.AsyncAppender">
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>WARN</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <discardingThreshold>0</discardingThreshold>
    <queueSize>${BUFFER_SIZE}</queueSize>
    <maxFlushTime>${FLUSH_TIMEOUT}</maxFlushTime>
    <includeCallerData>true</includeCallerData>
    <appender-ref ref="warn"/>
  </appender>

  <!-- ERROR 级别日志 -->
  <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_DIR}/error/error.log</file>
    <encoder>
      <pattern>${PATTERN}</pattern>
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${LOG_DIR}/error/error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <maxHistory>${MAX_HISTORY}</maxHistory>
      <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
    </rollingPolicy>
  </appender>
  <appender name="async_error" class="ch.qos.logback.classic.AsyncAppender">
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>ERROR</level>
      <onMatch>ACCEPT</onMatch>
      <onMismatch>DENY</onMismatch>
    </filter>
    <discardingThreshold>0</discardingThreshold>
    <queueSize>${BUFFER_SIZE}</queueSize>
    <maxFlushTime>${FLUSH_TIMEOUT}</maxFlushTime>
    <includeCallerData>true</includeCallerData>
    <appender-ref ref="error"/>
  </appender>

  <!-- 根日志器 -->
  <root level="INFO">
    <appender-ref ref="async_stdout"/>
    <appender-ref ref="async_trace"/>
    <appender-ref ref="async_debug"/>
    <appender-ref ref="async_info"/>
    <appender-ref ref="async_warn"/>
    <appender-ref ref="async_error"/>
  </root>
</configuration>