package cn.andata.ai.tools.dto;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.ai.util.json.schema.JsonSchemaGenerator;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonClassDescription(value = "轨迹走势响应体")
public class TrackInfoDTO extends BaseDTO<List<TrackInfoDTO.ResultItem>> {

    @Getter
    @JsonIgnore
    private static final String schema;

    static {
        schema = JsonSchemaGenerator.generateForType(TrackInfoDTO.class, JsonSchemaGenerator.SchemaOption.ALLOW_ADDITIONAL_PROPERTIES_BY_DEFAULT);
    }


    @Data
    @JsonClassDescription(value = "轨迹点详情")
    public static class ResultItem {

        @JsonPropertyDescription(value = "设备唯一标识")
        private String jdid;

        @JsonPropertyDescription(value = "时间")
        private String date;

        @JsonPropertyDescription(value = "经度")
        private String longitude;

        @JsonPropertyDescription(value = "纬度")
        private String latitude;

        @JsonPropertyDescription(value = "高德经度")
        private String gdLng;

        @JsonPropertyDescription(value = "高德纬度")
        private String gdLat;

        @JsonPropertyDescription(value = "轨迹来源（GPS WIFI_SSID CELL IP）")
        private String coordinateSource;

        @JsonPropertyDescription(value = "国家")
        private String country;

        @JsonPropertyDescription(value = "省份")
        private String province;

        @JsonPropertyDescription(value = "城市")
        private String city;

        @JsonPropertyDescription(value = "地区")
        private String district;

        @JsonPropertyDescription(value = "镇")
        private String town;

        @JsonPropertyDescription(value = "村")
        private String village;

        @JsonPropertyDescription(value = "地址")
        private String address;

        @JsonPropertyDescription(value = "WiFi名称")
        private String ssid;

        @JsonPropertyDescription(value = "wifi字段")
        private String wifi;

        @JsonPropertyDescription(value = "POI")
        private String poi;

        @JsonPropertyDescription(value = "POI类型")
        private String poiType;

        @JsonPropertyDescription(value = "大区号 LAC")
        private String locationAreaCode;

        @JsonPropertyDescription(value = "国家码 MCC")
        private String mobileCountryCode;

        @JsonPropertyDescription(value = "运营商 MNC")
        private String mobileNetworkCode;

        @JsonPropertyDescription(value = "9位geohash")
        private String geohash;

        @JsonPropertyDescription(value = "路")
        private String road;

        @JsonPropertyDescription(value = "IP地址")
        private String ip;
    }

}