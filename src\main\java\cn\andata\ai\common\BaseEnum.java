package cn.andata.ai.common;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基础枚举类，所有枚举实现此类，此类定义了getCode()和getMsg()两个方法
 * 通过枚举类型和code值获取对应的枚举类型，在springmvc中支持接受枚举类类参数（Content-Type: application/x-www-form-urlencoded)
 * RequestBody参数直接在对应的枚举类使用JsonCreator注解就行
 * T code的类型
 *
 * <AUTHOR>
 * @since Created in 2021/5/17 12:22
 */
@SuppressWarnings("all")
public interface BaseEnum<T> {

    /**
     * 通过枚举类型和code值获取对应的枚举类型
     *
     * @param enumType
     * @param code
     * @param <E>
     */
    static <E extends BaseEnum> E get(Class<? extends BaseEnum> enumType, String code) {
        if (enumType == null || code == null) {
            return null;
        }
        E[] enumConstants = (E[]) enumType.getEnumConstants();
        if (enumConstants == null) {
            return null;
        }
        for (E enumConstant : enumConstants) {
            Object enumCode = enumConstant.getValue();
            if (enumCode == null) {
                if (code == null) {
                    return enumConstant;
                }
            } else if (code.equalsIgnoreCase(enumCode.toString())) {
                return enumConstant;
            }
        }
        return null;
    }

    /**
     * 将enum转换为list
     *
     * @param enumType
     * @param <E>
     */
    static <E extends BaseEnum> List<Map<String, Object>> enum2List(Class<? extends BaseEnum> enumType) {
        if (enumType == null) {
            return null;
        }
        E[] enumConstants = (E[]) enumType.getEnumConstants();
        if (enumConstants == null) {
            return null;
        }
        ArrayList<Map<String, Object>> res = new ArrayList<>();
        for (E bean : enumConstants) {
            Object code = bean.getValue();
            String msg = bean.getDesc();
            HashMap<String, Object> map = new HashMap<>();
            map.put("code", code);
            map.put("msg", msg);
            res.add(map);
        }
        return res;
    }

    T getValue();

    String getDesc();


}