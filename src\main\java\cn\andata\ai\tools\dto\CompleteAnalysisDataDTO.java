package cn.andata.ai.tools.dto;

import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 完整分析数据DTO - 包含所有数据源的综合信息
 *
 * <AUTHOR>
 * @since 2025/8/1
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompleteAnalysisDataDTO {

    @JsonPropertyDescription("目标手机号")
    private String phone;

    @JsonPropertyDescription("数据获取时间戳")
    private Long timestamp;

    @JsonPropertyDescription("基本信息数据 - 包含设备特征、位置信息、最新轨迹、IP信息、设备标签等")
    private BaseInfoDTO baseInfo;

    @JsonPropertyDescription("设备信息数据 - 包含历史设备列表、换机换卡记录、设备关联度等")
    private DeviceInfoDTO deviceInfo;

    @JsonPropertyDescription("轨迹信息数据 - 包含近3个月详细位置轨迹、坐标、地址、POI信息等")
    private TrackInfoDTO trackInfo;

    @JsonPropertyDescription("WiFi历史数据 - 包含近3个月WiFi连接和扫描历史、热点信息、位置信息等")
    private HistoryWiFiInfoDTO historyWifiInfo;

    @JsonPropertyDescription("应用信息数据 - 包含应用安装列表、分类、敏感标签、使用行为等")
    private AppInfoDTO appInfo;

    @JsonPropertyDescription("数据获取状态信息")
    private DataStatus dataStatus;

    /**
     * 数据获取状态
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataStatus {
        
        @JsonPropertyDescription("基本信息获取是否成功")
        private boolean baseInfoSuccess;
        
        @JsonPropertyDescription("设备信息获取是否成功")
        private boolean deviceInfoSuccess;
        
        @JsonPropertyDescription("轨迹信息获取是否成功")
        private boolean trackInfoSuccess;
        
        @JsonPropertyDescription("WiFi信息获取是否成功")
        private boolean historyWifiInfoSuccess;
        
        @JsonPropertyDescription("应用信息获取是否成功")
        private boolean appInfoSuccess;
        
        @JsonPropertyDescription("总体获取成功率")
        private double successRate;
        
        @JsonPropertyDescription("获取失败的数据源列表")
        private java.util.List<String> failedSources;
        
        @JsonPropertyDescription("数据获取总耗时（毫秒）")
        private long totalDurationMs;
    }

    /**
     * 获取JSON Schema
     */
    public static String getSchema() {
        return """
            {
              "type": "object",
              "properties": {
                "phone": {"type": "string", "description": "目标手机号"},
                "timestamp": {"type": "integer", "description": "数据获取时间戳"},
                "baseInfo": {"$ref": "#/definitions/BaseInfoDTO"},
                "deviceInfo": {"$ref": "#/definitions/DeviceInfoDTO"},
                "trackInfo": {"$ref": "#/definitions/TrackInfoDTO"},
                "historyWifiInfo": {"$ref": "#/definitions/HistoryWiFiInfoDTO"},
                "appInfo": {"$ref": "#/definitions/AppInfoDTO"},
                "dataStatus": {
                  "type": "object",
                  "properties": {
                    "baseInfoSuccess": {"type": "boolean"},
                    "deviceInfoSuccess": {"type": "boolean"},
                    "trackInfoSuccess": {"type": "boolean"},
                    "historyWifiInfoSuccess": {"type": "boolean"},
                    "appInfoSuccess": {"type": "boolean"},
                    "successRate": {"type": "number"},
                    "failedSources": {"type": "array", "items": {"type": "string"}},
                    "totalDurationMs": {"type": "integer"}
                  }
                }
              }
            }
            """;
    }
}
