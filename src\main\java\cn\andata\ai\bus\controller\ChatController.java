package cn.andata.ai.bus.controller;

import cn.andata.ai.tools.KeplerTools;
import cn.andata.ai.tools.dto.BaseInfoDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.util.json.JsonParser;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * 会话聊天
 *
 * <AUTHOR>
 * @since 2025/7/30 17:13
 */
@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
public class ChatController {

    private final ChatClient chatClient;

    private final KeplerTools keplerTools;

    @PostMapping(value = "/chat", produces = "text/html;charset=UTF-8")
    public Flux<String> chat(@Validated @RequestBody String prompt,
                             @RequestHeader(value = "chatId", required = false, defaultValue = "spring-ai-alibaba-playground-chat") String chatId) {
        return chatClient.prompt()
                .user(prompt)
                .stream()
                .content();

    }

    @GetMapping("/keplerTools")
    public String keplerTools(String phone) {
        BaseInfoDTO baseInfoDTO = keplerTools.baseInfo(phone);
        return JsonParser.toJson(baseInfoDTO);
    }

}