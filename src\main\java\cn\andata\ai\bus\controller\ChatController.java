package cn.andata.ai.bus.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * 会话聊天
 *
 * <AUTHOR>
 * @since 2025/7/30 17:13
 */
@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
public class ChatController {

    private final ChatClient chatClient;

    @PostMapping(value = "/chat", produces = "text/html;charset=UTF-8")
    public Flux<String> chat(@Validated @RequestBody String prompt,
                             @RequestHeader(value = "chatId", required = false, defaultValue = "spring-ai-alibaba-playground-chat") String chatId) {
        return chatClient.prompt()
                .user(prompt)
                .advisors(p -> p.param(ChatMemory.CONVERSATION_ID, chatId))
                .stream()
                .content();

    }

}